import User from "../models/userModel.js";
import Order from "../models/orderModel.js";
import Product from "../models/productModel.js";

const getAdminDashboard = async (req, res) => {
    try {
        // Get total counts
        const totalUsers = await User.countDocuments();
        const totalOrders = await Order.countDocuments();
        const totalProducts = await Product.countDocuments();
        
        // Calculate total revenue
        const revenueResult = await Order.aggregate([
            { $match: { status: { $ne: 'cancelled' } } },
            { $group: { _id: null, total: { $sum: "$totalAmount" } } }
        ]);
        const totalRevenue = revenueResult[0]?.total || 0;

        // Get recent orders
        const recentOrders = await Order.find()
            .populate('user', 'fullName email')
            .sort({ createdAt: -1 })
            .limit(10);

        return res.status(200).json({
            stats: {
                totalUsers,
                totalOrders,
                totalRevenue,
                totalProducts
            },
            recentOrders
        });
    } catch (error) {
        console.error('Admin dashboard error:', error);
        return res.status(500).json({ message: "Server error" });
    }
}

export { getAdminDashboard }
